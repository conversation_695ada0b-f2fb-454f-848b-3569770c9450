<!--
  ~ Copyright (C) 2024-2025 OpenAni and contributors.
  ~
  ~ 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
  ~ Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
  ~
  ~ https://github.com/open-ani/ani/blob/main/LICENSE
  -->

<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="Run Desktop (Hot Reload)" type="GradleRunConfiguration"
            factoryName="Gradle">
        <ExternalSystemSettings>
            <option name="executionName" />
            <option name="externalProjectPath" value="$PROJECT_DIR$" />
            <option name="externalSystemIdString" value="GRADLE" />
            <option name="scriptParameters" value="" />
            <option name="taskDescriptions">
                <list />
            </option>
            <option name="taskNames">
                <list>
                    <option value=":app:desktop:runHot" />
                </list>
            </option>
            <option name="vmOptions" />
        </ExternalSystemSettings>
        <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
        <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
        <DebugAllEnabled>false</DebugAllEnabled>
        <RunAsTest>false</RunAsTest>
        <method v="2" />
    </configuration>
</component>